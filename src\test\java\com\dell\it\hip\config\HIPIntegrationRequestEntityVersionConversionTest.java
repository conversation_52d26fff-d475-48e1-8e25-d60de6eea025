package com.dell.it.hip.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;

/**
 * Unit tests for BigDecimal to String version conversion in HIPIntegrationRequestEntity.
 * Tests the new version field conversion methods and backward compatibility.
 */
public class HIPIntegrationRequestEntityVersionConversionTest {

    private HIPIntegrationRequestEntity entity;

    @BeforeEach
    void setUp() {
        entity = new HIPIntegrationRequestEntity();
    }

    @Test
    void testSetVersionString_SimpleDecimalFormats() {
        // Test simple decimal version formats (should round-trip exactly)
        String[] simpleVersions = {
            "1.0", "2.1", "1", "2", "100", "0.1", "0.0.1"
        };

        for (String version : simpleVersions) {
            entity.setVersion(version);
            assertEquals(version, entity.getVersion(),
                "Simple decimal version should round-trip correctly for: " + version);
            assertNotNull(entity.getVersionBigDecimal(),
                "BigDecimal version should not be null for: " + version);
        }
    }

    @Test
    void testSetVersionString_SemanticVersionFormats() {
        // Test semantic version formats (will be converted to decimal format)
        String[][] semanticVersions = {
            {"1.0.0", "1.0000"},   // 1.0.0 -> 1.0000
            {"1.2.3", "1.0203"},   // 1.2.3 -> 1.0203
            {"10.5.3", "10.0503"}, // 10.5.3 -> 10.0503
            {"2.10.15", "2.1015"}  // 2.10.15 -> 2.1015
        };

        for (String[] versionPair : semanticVersions) {
            String input = versionPair[0];
            String expected = versionPair[1];

            entity.setVersion(input);
            assertEquals(expected, entity.getVersion(),
                "Semantic version " + input + " should convert to " + expected);
            assertNotNull(entity.getVersionBigDecimal(),
                "BigDecimal version should not be null for: " + input);
        }
    }

    @Test
    void testSetVersionString_NullValue() {
        // Test null handling
        entity.setVersion(null);
        assertNull(entity.getVersion());
        assertNull(entity.getVersionBigDecimal());
    }

    @Test
    void testSetVersionString_InvalidFormats() {
        // Test invalid version formats
        String[] invalidVersions = {
            "abc", "1.0.x", "v1.0", "1.0-SNAPSHOT", "", "  ", "1..0"
        };

        for (String invalidVersion : invalidVersions) {
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
                entity.setVersion(invalidVersion);
            }, "Should throw exception for invalid version: " + invalidVersion);

            assertTrue(exception.getMessage().contains("Invalid version format"));
            assertTrue(exception.getMessage().contains(invalidVersion));
        }
    }

    @Test
    void testSetVersionString_EdgeCaseValidFormats() {
        // Test edge cases that are actually valid
        String[] edgeCaseVersions = {".1", "0.1"};

        for (String version : edgeCaseVersions) {
            // These should not throw exceptions, though they may be normalized
            assertDoesNotThrow(() -> {
                entity.setVersion(version);
                assertNotNull(entity.getVersion());
            }, "Should not throw exception for edge case version: " + version);
        }
    }

    @Test
    void testSetVersionString_InvalidTrailingDot() {
        // Test versions with trailing dots (should be invalid)
        String[] invalidVersions = {"10.", "1.0."};

        for (String version : invalidVersions) {
            assertThrows(IllegalArgumentException.class, () -> {
                entity.setVersion(version);
            }, "Should throw exception for invalid version with trailing dot: " + version);
        }
    }

    @Test
    void testGetVersionString_FromBigDecimal() {
        // Test getting string version from BigDecimal
        BigDecimal[] testValues = {
            new BigDecimal("1.0"), new BigDecimal("2.1"), new BigDecimal("10.5"),
            new BigDecimal("1"), new BigDecimal("0.1"), new BigDecimal("100.25")
        };

        for (BigDecimal bigDecimalVersion : testValues) {
            entity.setVersionBigDecimal(bigDecimalVersion);
            String stringVersion = entity.getVersion();
            assertNotNull(stringVersion);
            assertEquals(bigDecimalVersion.toPlainString(), stringVersion);
        }
    }

    @Test
    void testGetVersionString_NullBigDecimal() {
        // Test null BigDecimal handling
        entity.setVersionBigDecimal(null);
        assertNull(entity.getVersion());
    }

    @Test
    void testVersionRoundTrip_SimpleDecimalVersions() {
        // Test complete round-trip conversion for simple decimal versions
        String[] simpleVersions = {
            "1.0", "2.1", "10.5", "1", "0.1", "100.25"
        };

        for (String originalVersion : simpleVersions) {
            entity.setVersion(originalVersion);
            String retrievedVersion = entity.getVersion();
            assertEquals(originalVersion, retrievedVersion,
                "Round-trip conversion should preserve simple decimal version: " + originalVersion);
        }
    }

    @Test
    void testVersionRoundTrip_SemanticVersions() {
        // Test round-trip conversion for semantic versions (will be converted)
        String[][] semanticVersions = {
            {"1.2.3", "1.0203"},
            {"2.0.0", "2.0000"},
            {"10.5.3", "10.0503"}
        };

        for (String[] versionPair : semanticVersions) {
            String input = versionPair[0];
            String expected = versionPair[1];

            entity.setVersion(input);
            String retrievedVersion = entity.getVersion();
            assertEquals(expected, retrievedVersion,
                "Semantic version " + input + " should convert to " + expected);
        }
    }

    @Test
    void testVersionRoundTrip_BigDecimalToStringToBigDecimal() {
        // Test round-trip from BigDecimal perspective
        BigDecimal[] testValues = {
            new BigDecimal("1.0"), new BigDecimal("2.1"), new BigDecimal("10.5"),
            new BigDecimal("1"), new BigDecimal("0.1")
        };

        for (BigDecimal originalBigDecimal : testValues) {
            entity.setVersionBigDecimal(originalBigDecimal);
            String stringVersion = entity.getVersion();
            entity.setVersion(stringVersion);
            BigDecimal retrievedBigDecimal = entity.getVersionBigDecimal();
            
            assertEquals(0, originalBigDecimal.compareTo(retrievedBigDecimal), 
                "Round-trip BigDecimal conversion should preserve value: " + originalBigDecimal);
        }
    }

    @Test
    void testVersionPrecisionHandling() {
        // Test that precision is maintained
        entity.setVersion("1.00");
        assertEquals("1.00", entity.getVersion());
        
        entity.setVersion("10.50");
        assertEquals("10.50", entity.getVersion());
        
        entity.setVersion("0.10");
        assertEquals("0.10", entity.getVersion());
    }

    @Test
    void testVersionScaleHandling() {
        // Test different scales
        entity.setVersion("1");
        assertEquals("1", entity.getVersion());
        
        entity.setVersion("1.0");
        assertEquals("1.0", entity.getVersion());
        
        entity.setVersion("1.00");
        assertEquals("1.00", entity.getVersion());
    }

    @Test
    void testBackwardCompatibility() {
        // Test that existing application code patterns still work
        entity.setVersion("1.0");
        
        // These are typical operations that application code might do
        String version = entity.getVersion();
        assertNotNull(version);
        assertEquals("1.0", version);
        
        // Version comparison (typical application logic)
        entity.setVersion("2.0");
        assertTrue(entity.getVersion().compareTo("1.0") > 0);
        
        entity.setVersion("1.0");
        assertTrue(entity.getVersion().compareTo("2.0") < 0);
        
        entity.setVersion("1.0");
        assertEquals(0, entity.getVersion().compareTo("1.0"));
    }

    @Test
    void testVersionEquality() {
        // Test that semantically equivalent versions are handled correctly
        entity.setVersion("1.0");
        String version1 = entity.getVersion();
        
        entity.setVersion("1.00");
        String version2 = entity.getVersion();
        
        // Note: These will be different strings due to BigDecimal precision preservation
        // This is expected behavior - the application should handle version comparison logic
        assertNotEquals(version1, version2);
        assertEquals("1.0", version1);
        assertEquals("1.00", version2);
    }

    @Test
    void testLargeVersionNumbers() {
        // Test handling of large version numbers
        String largeVersion = "999999.999999";
        entity.setVersion(largeVersion);
        assertEquals(largeVersion, entity.getVersion());
        assertNotNull(entity.getVersionBigDecimal());
    }

    @Test
    void testZeroVersions() {
        // Test zero and near-zero versions
        String[] zeroVersions = {"0", "0.0", "0.1", "0.01", "0.001"};
        
        for (String zeroVersion : zeroVersions) {
            entity.setVersion(zeroVersion);
            assertEquals(zeroVersion, entity.getVersion());
            assertNotNull(entity.getVersionBigDecimal());
        }
    }

    @Test
    void testVersionWithLeadingZeros() {
        // Test versions with leading zeros (should be preserved as strings)
        entity.setVersion("01.0");
        // BigDecimal will normalize this, so we expect "1.0"
        assertEquals("1.0", entity.getVersion());
        
        entity.setVersion("001.000");
        // BigDecimal will normalize this, so we expect "1.000"
        assertEquals("1.000", entity.getVersion());
    }

    @Test
    void testDirectBigDecimalAccess() {
        // Test direct BigDecimal getter/setter (for JPA use)
        BigDecimal testValue = new BigDecimal("2.5");
        entity.setVersionBigDecimal(testValue);
        
        assertEquals(testValue, entity.getVersionBigDecimal());
        assertEquals("2.5", entity.getVersion());
    }

    @Test
    void testExceptionMessageQuality() {
        // Test that exception messages are helpful
        String invalidVersion = "invalid-version";
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            entity.setVersion(invalidVersion);
        });
        
        String message = exception.getMessage();
        assertTrue(message.contains("Invalid version format"));
        assertTrue(message.contains(invalidVersion));
        assertTrue(message.contains("valid decimal number"));
        assertTrue(message.contains("1.0") || message.contains("examples"));
    }
}
