package com.dell.it.hip.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.controller.dto.IntegrationStatusResponse;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.util.ThrottleSettings;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * Central controller for HIP Integration platform admin APIs. Provides
 * comprehensive management capabilities for integration lifecycle, throttling,
 * and monitoring.
 */
@RestController
@RequestMapping("/hip/management")
@Tag(name = "Integration Management", description = "HIP Integration platform administration and lifecycle management")
@SecurityRequirement(name = "bearerAuth")
public class HIPIntegrationManagementController {

	@Autowired
	private HIPIntegrationOrchestrationService orchestrationService;

	@Autowired
	private ServiceManager serviceManager;

	@Autowired
	private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

	@Value("${service.manager.name}")
	private String serviceManagerName;

	// === HIP Integration lifecycle ===

	@Operation(summary = "Register new integration", description = "Register a new HIP integration with the platform. Requires ADMIN role.")
	@ApiResponses(value = {
	        @ApiResponse(responseCode = "200", description = "Integration registered successfully"),
	        @ApiResponse(responseCode = "400", description = "Invalid integration configuration or registration failed"),
	        @ApiResponse(responseCode = "403", description = "Access denied - ADMIN role required"),
	        @ApiResponse(responseCode = "500", description = "Internal server error during registration")
	    })
	@PostMapping("/register")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> register(
			@Parameter(description = "Integration configuration request", required = true) @Valid @RequestBody HIPIntegrationRequest request) {
		orchestrationService.registerHIPIntegration(request);
		return ResponseEntity.ok("HIPIntegration registered");
	}

	@Operation(summary = "Unregister integration", description = "Remove an integration from the platform. Requires ADMIN role.")
	@ApiResponses(value = { @ApiResponse(responseCode = "200", description = "Integration unregistered successfully"),
			@ApiResponse(responseCode = "400", description = "Unregistration failed"),
			@ApiResponse(responseCode = "404", description = "Integration not found"),
			@ApiResponse(responseCode = "403", description = "Access denied - ADMIN role required"),
			@ApiResponse(responseCode = "500", description = "Internal server error during unregistration") })
	@DeleteMapping("/{name}/{version}")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> unregister(
			@Parameter(description = "Integration name", required = true) @PathVariable String name,
			@Parameter(description = "Integration version", required = true) @PathVariable String version) {
		orchestrationService.unregisterHIPIntegration(name, version);
		return ResponseEntity.ok("Unregistered");
	}

	@Operation(summary = "Pause integration", description = "Pause an active integration. Requires ADMIN role.")
	@ApiResponses(value = {
	        @ApiResponse(responseCode = "200", description = "Integration paused successfully"),
	        @ApiResponse(responseCode = "400", description = "Pause operation failed"),
	        @ApiResponse(responseCode = "404", description = "Integration not found"),
	        @ApiResponse(responseCode = "403", description = "Access denied - ADMIN role required"),
	        @ApiResponse(responseCode = "500", description = "Internal server error during pause")
	    })
	@PutMapping("/{name}/{version}/pause")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> pause(
			@Parameter(description = "Integration name", required = true) @PathVariable String name,
			@Parameter(description = "Integration version", required = true) @PathVariable String version) {
		orchestrationService.pauseHIPIntegration(name, version);
		return ResponseEntity.ok("Paused");
	}

	@Operation(summary = "Resume integration", description = "Resume a paused integration. Requires ADMIN role.")
	@ApiResponses(value = {
	        @ApiResponse(responseCode = "200", description = "Integration resumed successfully"),
	        @ApiResponse(responseCode = "400", description = "Resume operation failed"),
	        @ApiResponse(responseCode = "404", description = "Integration not found"),
	        @ApiResponse(responseCode = "403", description = "Access denied - ADMIN role required"),
	        @ApiResponse(responseCode = "500", description = "Internal server error during resume")
	    })
	@PutMapping("/{name}/{version}/resume")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> resume(
			@Parameter(description = "Integration name", required = true) @PathVariable String name,
			@Parameter(description = "Integration version", required = true) @PathVariable String version) {
		orchestrationService.resumeHIPIntegration(name, version);
		return ResponseEntity.ok("Resumed");
	}

	// === Throttling ===

	@PostMapping("/{name}/{version}/throttle")
	public ResponseEntity<?> throttle(@PathVariable String name, @PathVariable String version,
			@RequestBody ThrottleSettings settings) {
		orchestrationService.applyThrottle(name, version, settings);
		return ResponseEntity.ok("Throttle applied");
	}

	@DeleteMapping("/{name}/{version}/throttle")
	public ResponseEntity<?> removeThrottle(@PathVariable String name, @PathVariable String version) {
		orchestrationService.removeThrottle(name, version);
		return ResponseEntity.ok("Throttle removed");
	}

	// ==== Per-adapter/handler controls ====

	@PutMapping("/{name}/{version}/adapter/{adapterRef}/pause")
	public ResponseEntity<?> pauseAdapter(@PathVariable String name, @PathVariable String version,
			@PathVariable String adapterRef) {
		orchestrationService.pauseAdapter(name, version, adapterRef);
		return ResponseEntity.ok("Adapter paused");
	}

	@PutMapping("/{name}/{version}/adapter/{adapterRef}/resume")
	public ResponseEntity<?> resumeAdapter(@PathVariable String name, @PathVariable String version,
			@PathVariable String adapterRef) {
		orchestrationService.resumeAdapter(name, version, adapterRef);
		return ResponseEntity.ok("Adapter resumed");
	}

	@PutMapping("/{name}/{version}/handler/{handlerRef}/pause")
	public ResponseEntity<?> pauseHandler(@PathVariable String name, @PathVariable String version,
			@PathVariable String handlerRef) {
		orchestrationService.pauseHandler(name, version, handlerRef);
		return ResponseEntity.ok("Handler paused");
	}

	@PutMapping("/{name}/{version}/handler/{handlerRef}/resume")
	public ResponseEntity<?> resumeHandler(@PathVariable String name, @PathVariable String version,
			@PathVariable String handlerRef) {
		orchestrationService.resumeHandler(name, version, handlerRef);
		return ResponseEntity.ok("Handler resumed");
	}

	@PostMapping("/strictorder/manualRelease")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> strictOrderManualRelease(@RequestParam String integrationName,
			@RequestParam String version, @RequestParam String stepPropertyRef,
			@RequestParam List<String> orderingKeyValues, // e.g. [customerId-123, orderType-xyz]
			@RequestParam(required = false) Long uptoSeq) {
		int count = orchestrationService.strictOrderManualRelease(integrationName, version, stepPropertyRef,
				orderingKeyValues, uptoSeq);
		return ResponseEntity.ok("Released " + count + " messages for partition " + orderingKeyValues);
	}
	// === SFTP force-poll callback ===

	@PostMapping("/sftp/{integrationName}/{version}/{adapterId}/fileReady")
	public ResponseEntity<?> sftpFileReadyCallback(@PathVariable String integrationName, @PathVariable String version,
			@PathVariable String adapterId) {
		orchestrationService.triggerSftpForcePoll(integrationName, version, adapterId);
		return ResponseEntity.ok("Force poll triggered for SFTP adapter.");
	}

	// === HIP Inventory/status APIs ===

	@GetMapping("/all")
	public List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> getAll() {
		return orchestrationService.getAllHIPIntegrationsWithStatus();
	}

	@GetMapping("/{name}/{version}/status")
	public ResponseEntity<String> status(@PathVariable String name, @PathVariable String version) {
		String status = orchestrationService.getAllHIPIntegrationsWithStatus().stream()
				.filter(i -> i.getHipIntegrationName().equals(name) && i.getVersion().equals(version))
				.map(i -> i.getStatus().name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());

		return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(status);
	}

	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Integration status information retrieved successfully"),
			@ApiResponse(responseCode = "404", description = "No integrations found for the specified name"),
			@ApiResponse(responseCode = "500", description = "Internal server error during retrieval") })
	@GetMapping("/{name}/status")
	public ResponseEntity<IntegrationStatusResponse> getIntegrationStatus(@PathVariable String name) {
		List<HIPIntegrationDefinition> definitions = serviceManager.getDefinitionsByName(name);
		if (definitions.isEmpty())
			return ResponseEntity.notFound().build();

		// Get status information for each version using direct Redis access
		List<IntegrationStatusResponse.IntegrationVersionStatus> integrations = definitions.stream()
				.map(def -> {
					IntegrationStatus status = hipIntegrationRuntimeService.getHIPIntegrationStatus(
							serviceManagerName, def.getHipIntegrationName(), def.getVersion());
					return new IntegrationStatusResponse.IntegrationVersionStatus(
							def.getHipIntegrationName(), def.getVersion(), status);
				})
				.collect(Collectors.toList());

		IntegrationStatusResponse response = new IntegrationStatusResponse(integrations);
		return ResponseEntity.ok(response);
	}

	// === (Optional) Diagnostics/Config APIs ===

	@GetMapping("/{name}/{version}/definition")
	public ResponseEntity<?> getDefinition(@PathVariable String name, @PathVariable String version) {
		HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(name, version);
		if (def == null)
			return ResponseEntity.notFound().build();
		return ResponseEntity.ok(def);
	}



	@PutMapping("/{name}/{version}/handler/{handlerRef}/shutdown")
	public ResponseEntity<?> shutdownHandler(@PathVariable String name, @PathVariable String version,
			@PathVariable String handlerRef) {
		orchestrationService.shutdownHandler(name, version, handlerRef);
		return ResponseEntity.ok("Handler shutdown");
	}

	@PutMapping("/{name}/{version}/shutdown")
	public ResponseEntity<?> shutdownIntegration(@PathVariable String name, @PathVariable String version) {
		orchestrationService.shutdownHIPIntegration(name, version);
		return ResponseEntity.ok("Integration shutdown");
	}

	@GetMapping("/cluster/events/recent")
	public List<HIPClusterEvent> getRecentClusterEvents() {
		// Implement a rolling buffer/cache for events, or expose directly from cluster
		// coordination service.
		return orchestrationService.getRecentClusterEvents();
	}

	@Operation(summary = "Refresh rules cache", description = "Reloads the specified versioned rules from Redis into the in-memory rule cache.")
	@ApiResponses(value = { @ApiResponse(responseCode = "200", description = "Rules cache refreshed"),
			@ApiResponse(responseCode = "400", description = "No rules specified") })
	@PostMapping("/refresh/integration")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> refreshIntegrationRules(@RequestParam String serviceManagerName,
			@RequestParam String integrationName, @RequestParam String version) {
		orchestrationService.refreshIntegrationRules(serviceManagerName, integrationName, version);
		return ResponseEntity.ok("Integration rules refreshed.");
	}

	// Refresh rules for property sheet-based steps
	// (MappingTransformer/FlowTargetsRouting)
	@PostMapping("/refresh/explicit")
	@PreAuthorize("hasRole('ADMIN')")
	public ResponseEntity<?> refreshExplicitRules(@RequestBody List<RuleRef> ruleRefs) {
		orchestrationService.refreshExplicitRules(ruleRefs);
		return ResponseEntity.ok("Explicit rules refreshed.");
	}

}