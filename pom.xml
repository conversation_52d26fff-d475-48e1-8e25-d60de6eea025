<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.emc.it.eis</groupId>
		<artifactId>cic-rest-parent</artifactId>
		<version>6.2.0.RELEASE</version>
	</parent>
	<groupId>com.dell.it.hip</groupId>
	<packaging>jar</packaging>
	<artifactId>${CI_PROJECT_NAME}</artifactId>
  	<version>${PROJECT_VERSION}</version>
  	<name>${CI_PROJECT_NAME}</name>
	<description>Dynamic, cluster-aware Spring Integration platform with flow
		control, retry, tracing</description>

	<dependencies>
		<!-- Spring Core and Web -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>

		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>

		</dependency>

		<!-- Spring Integration Core -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-core</artifactId>
		</dependency>

		<!-- Kafka -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-kafka</artifactId>

		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>

		</dependency>

		<!-- RabbitMQ -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-amqp</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.amqp</groupId>
			<artifactId>spring-rabbit</artifactId>

		</dependency>

		<!-- IBM MQ (JMS) -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-jms</artifactId>
		</dependency>
		<!-- JMS API -->

		<!-- SFTP and NAS (File) -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-file</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-sftp</artifactId>

		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>${jsch.version}</version>
		</dependency>

		<!-- Validation -->
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>

		</dependency>

		<!-- Retry and Messaging -->
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-messaging</artifactId>

		</dependency>

		<!-- JSON / Jackson -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>

		</dependency>

		<!-- OpenTelemetry -->

		<!-- Spring Boot Admin & Logging -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>

		</dependency>

		<!-- Testing -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>

			<scope>test</scope>
		</dependency>

		<!-- STAEDI for EDI X12 validation -->
		<dependency>
			<groupId>io.xlate</groupId>
			<artifactId>staedi</artifactId>
			<version>${staedi.version}</version>

		</dependency>

		<!-- Commons IO for file utilities -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>

		</dependency>

		<!-- Commons Compress for GZIP/ZIP -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>

		</dependency>

		<!-- Micrometer Core for metrics -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<!-- nas dependencies -->
		<dependency>
			<groupId>eu.agno3.jcifs</groupId>
			<artifactId>jcifs-ng</artifactId>
			<version>${jcifs-ng.version}</version>
		</dependency>
		<!-- SFTP integration is already declared above -->
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-core</artifactId>
			<version>${sshd.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-common</artifactId>
			<version>${sshd.version}</version>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/com.ibm.mq/mq-jms-spring-boot-starter -->
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>mq-jms-spring-boot-starter</artifactId>
			</dependency>

		<!-- TestContainers -->
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>testcontainers</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>kafka</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>${redisson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>rabbitmq</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>postgresql</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.redis</groupId>
			<artifactId>testcontainers-redis</artifactId>
			<version>2.2.2</version>
			<scope>test</scope>
		</dependency>


		<!-- Kafka Test -->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Spring Security -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<!-- Spring Security Test -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Swagger/OpenAPI Documentation -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
		</dependency>

		<!-- WebJars for Swagger UI -->
		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>webjars-locator-core</artifactId>
		</dependency>

		<!-- H2 Database for Testing -->
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- Hibernate Validator -->
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
		</dependency>

		<dependency>
			<groupId>com.networknt</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>${json-schema-validator.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-kafka</artifactId>
			<!-- or latest compatible with your Spring Boot version -->
		</dependency>

		<!-- JWT Support -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>${jjwt.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${jjwt.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${jjwt.version}</version>
			<scope>runtime</scope>
		</dependency>

		<!-- Circuit Breaker -->
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-spring-boot3</artifactId>
			</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-circuitbreaker</artifactId>
			</dependency>

		<!-- Enhanced Metrics -->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<!-- PostgreSQL Driver -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc11</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		
         <!-- Message Logging Builder -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>transaction-monitoring-logging-msgbuilder</artifactId>
			</dependency>
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>
				transaction-monitoring-logging-exporter
			</artifactId>		
		</dependency>
		
		<!-- Contivo Support -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>contivo-support</artifactId>
			<version>${contivo6.7.version}</version>
			<exclusions>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

        <!-- Oauth2 Server -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>oauth2-server</artifactId>
		</dependency>
		<dependency>
 			 <groupId>io.opentelemetry</groupId>
  			 <artifactId>opentelemetry-exporter-logging</artifactId>
		</dependency>
	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>

			<!-- JaCoCo Code Coverage -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>check</id>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>CLASS</element>
									<limits>
										<limit>
											<counter>LINE</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.00</minimum>
										</limit>
									</limits>
								</rule>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Surefire for unit tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>			
				<configuration>
					<includes>
						<include>**/*Test.java</include>
						<include>**/*Tests.java</include>
					</includes>
				</configuration>
			</plugin>

			<!-- Failsafe for integration tests -->
			<!--<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>	
				<configuration>
					<includes>
						<include>**/*IT.java</include>
						<include>**/*IntegrationTest.java</include>
					</includes>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
		</plugins>
	</build>
	<properties>
		<jsch.version>0.1.55</jsch.version>
		<staedi.version>1.25.3</staedi.version>
		<mq-jms-spring-boot-starter.version>3.4.3</mq-jms-spring-boot-starter.version>
		<testcontainers.version>1.21.0</testcontainers.version>
		<jjwt.version>0.12.6</jjwt.version>
		<resilience4j.version>2.2.0</resilience4j.version>
		<jacoco.version>0.8.12</jacoco.version>
		<maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
		<maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
		<json-schema-validator.version>1.5.6</json-schema-validator.version>
		<sshd.version>2.11.0</sshd.version>
		<jcifs-ng.version>2.1.9</jcifs-ng.version>
		<redisson.version>3.35.0</redisson.version>
		<contivo6.7.version>6.7-6.1.0.RELEASE</contivo6.7.version>
	</properties>

</project>
