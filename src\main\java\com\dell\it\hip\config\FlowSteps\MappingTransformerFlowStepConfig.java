package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MappingTransformerFlowStepConfig extends FlowStepConfig {

	private List<DocTypeMappingTransformerConfig> docTypeConfigs = new ArrayList<>();
    private DefaultMappingTransformerConfig defaultConfig;
    
    @Data
    public static class DocTypeMappingTransformerConfig extends DocTypeConfig {
        private MappingBehavior behavior = MappingBehavior.TRANSFORM;
        private String transformerRef;
        private List<RuleRef> ruleRefs;
        private boolean isDbBacked;
    }

    @Data
    public static class DefaultMappingTransformerConfig {
    	private MappingBehavior behavior = MappingBehavior.TRANSFORM;
    	private String transformerRef;
        private List<RuleRef> ruleRefs;
        private boolean isDbBacked;
    }

    public enum MappingBehavior { TRANSFORM, SKIP, TERMINATE }

}
